--
-- PostgreSQL database dump
--

-- Dumped from database version 14.13 (Debian 14.13-1.pgdg120+1)
-- Dumped by pg_dump version 14.13 (Debian 14.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: citext; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS citext WITH SCHEMA public;


--
-- Name: EXTENSION citext; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION citext IS 'data type for case-insensitive character strings';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: achievements; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.achievements (
    id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    type text NOT NULL,
    target integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.achievements OWNER TO olzzhas;

--
-- Name: achievements_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.achievements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.achievements_id_seq OWNER TO olzzhas;

--
-- Name: achievements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.achievements_id_seq OWNED BY public.achievements.id;


--
-- Name: authorization_tokens; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.authorization_tokens (
    id integer NOT NULL,
    user_id bigint NOT NULL,
    refresh_token text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.authorization_tokens OWNER TO olzzhas;

--
-- Name: authorization_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.authorization_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.authorization_tokens_id_seq OWNER TO olzzhas;

--
-- Name: authorization_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.authorization_tokens_id_seq OWNED BY public.authorization_tokens.id;


--
-- Name: expo_tokens; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.expo_tokens (
    id integer NOT NULL,
    token text NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.expo_tokens OWNER TO olzzhas;

--
-- Name: expo_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.expo_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.expo_tokens_id_seq OWNER TO olzzhas;

--
-- Name: expo_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.expo_tokens_id_seq OWNED BY public.expo_tokens.id;


--
-- Name: modules; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.modules (
    id integer NOT NULL,
    name text NOT NULL,
    theory_ids integer[] NOT NULL,
    question_ids integer[] NOT NULL,
    level integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    pre_requisite_ids integer[]
);


ALTER TABLE public.modules OWNER TO olzzhas;

--
-- Name: modules_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.modules_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.modules_id_seq OWNER TO olzzhas;

--
-- Name: modules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.modules_id_seq OWNED BY public.modules.id;


--
-- Name: progress; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.progress (
    id integer NOT NULL,
    user_id integer NOT NULL,
    module_id integer NOT NULL,
    mistaken_question_ids integer[] NOT NULL,
    "time" interval NOT NULL,
    try_count integer NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.progress OWNER TO olzzhas;

--
-- Name: progress_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.progress_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.progress_id_seq OWNER TO olzzhas;

--
-- Name: progress_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.progress_id_seq OWNED BY public.progress.id;


--
-- Name: question_words; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.question_words (
    question_id integer NOT NULL,
    word_id integer NOT NULL,
    sequence_order integer
);


ALTER TABLE public.question_words OWNER TO olzzhas;

--
-- Name: questions; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.questions (
    id integer NOT NULL,
    type text NOT NULL,
    correct_answer jsonb,
    image_url text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.questions OWNER TO olzzhas;

--
-- Name: questions_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.questions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.questions_id_seq OWNER TO olzzhas;

--
-- Name: questions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.questions_id_seq OWNED BY public.questions.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);


ALTER TABLE public.schema_migrations OWNER TO olzzhas;

--
-- Name: sentences; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.sentences (
    id integer NOT NULL,
    kaz_plaintext text NOT NULL,
    rus_plaintext text NOT NULL,
    audio_url text
);


ALTER TABLE public.sentences OWNER TO olzzhas;

--
-- Name: sentences_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.sentences_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.sentences_id_seq OWNER TO olzzhas;

--
-- Name: sentences_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.sentences_id_seq OWNED BY public.sentences.id;


--
-- Name: theories; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.theories (
    id integer NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    module_id integer,
    tags text[],
    examples_ids integer[],
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.theories OWNER TO olzzhas;

--
-- Name: theories_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.theories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.theories_id_seq OWNER TO olzzhas;

--
-- Name: theories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.theories_id_seq OWNED BY public.theories.id;


--
-- Name: tokens; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.tokens (
    hash bytea NOT NULL,
    user_id bigint NOT NULL,
    expiry timestamp(0) with time zone NOT NULL,
    scope text NOT NULL
);


ALTER TABLE public.tokens OWNER TO olzzhas;

--
-- Name: user_achievements; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.user_achievements (
    id integer NOT NULL,
    user_id integer NOT NULL,
    achievement_id integer NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    achieved boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.user_achievements OWNER TO olzzhas;

--
-- Name: user_achievements_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.user_achievements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.user_achievements_id_seq OWNER TO olzzhas;

--
-- Name: user_achievements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.user_achievements_id_seq OWNED BY public.user_achievements.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    created_at timestamp(0) with time zone DEFAULT now() NOT NULL,
    name text NOT NULL,
    image_url text,
    surname text NOT NULL,
    email public.citext NOT NULL,
    password_hash bytea NOT NULL,
    activated boolean NOT NULL
);


ALTER TABLE public.users OWNER TO olzzhas;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO olzzhas;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: words; Type: TABLE; Schema: public; Owner: olzzhas
--

CREATE TABLE public.words (
    id integer NOT NULL,
    kaz_plaintext text NOT NULL,
    rus_plaintext text NOT NULL,
    audio_url text
);


ALTER TABLE public.words OWNER TO olzzhas;

--
-- Name: words_id_seq; Type: SEQUENCE; Schema: public; Owner: olzzhas
--

CREATE SEQUENCE public.words_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.words_id_seq OWNER TO olzzhas;

--
-- Name: words_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: olzzhas
--

ALTER SEQUENCE public.words_id_seq OWNED BY public.words.id;


--
-- Name: achievements id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.achievements ALTER COLUMN id SET DEFAULT nextval('public.achievements_id_seq'::regclass);


--
-- Name: authorization_tokens id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.authorization_tokens ALTER COLUMN id SET DEFAULT nextval('public.authorization_tokens_id_seq'::regclass);


--
-- Name: expo_tokens id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.expo_tokens ALTER COLUMN id SET DEFAULT nextval('public.expo_tokens_id_seq'::regclass);


--
-- Name: modules id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.modules ALTER COLUMN id SET DEFAULT nextval('public.modules_id_seq'::regclass);


--
-- Name: progress id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.progress ALTER COLUMN id SET DEFAULT nextval('public.progress_id_seq'::regclass);


--
-- Name: questions id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.questions ALTER COLUMN id SET DEFAULT nextval('public.questions_id_seq'::regclass);


--
-- Name: sentences id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.sentences ALTER COLUMN id SET DEFAULT nextval('public.sentences_id_seq'::regclass);


--
-- Name: theories id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.theories ALTER COLUMN id SET DEFAULT nextval('public.theories_id_seq'::regclass);


--
-- Name: user_achievements id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.user_achievements ALTER COLUMN id SET DEFAULT nextval('public.user_achievements_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: words id; Type: DEFAULT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.words ALTER COLUMN id SET DEFAULT nextval('public.words_id_seq'::regclass);


--
-- Data for Name: achievements; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.achievements (id, name, description, type, target, created_at, updated_at) FROM stdin;
1	7 дней темпа	 	temp	7	2024-12-04 10:57:20.468994	2024-12-04 10:57:20.468994
2	14 дней темпа	 	temp	14	2024-12-04 10:57:26.923648	2024-12-04 10:57:26.923648
3	30 дней темпа	 	temp	30	2024-12-04 10:57:32.208325	2024-12-04 10:57:32.208325
4	90 дней темпа	 	temp	90	2024-12-04 10:57:39.741605	2024-12-04 10:57:39.741605
5	5 занятий	 	progress	5	2024-12-04 10:58:22.85122	2024-12-04 10:58:22.85122
6	10 занятий	 	progress	10	2024-12-04 10:58:26.990384	2024-12-04 10:58:26.990384
7	15 занятий	 	progress	15	2024-12-04 10:58:31.569296	2024-12-04 10:58:31.569296
8	30 занятий	 	progress	30	2024-12-04 10:58:39.956057	2024-12-04 10:58:39.956057
\.


--
-- Data for Name: authorization_tokens; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.authorization_tokens (id, user_id, refresh_token, created_at) FROM stdin;
2	2	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJleHAiOjE3MzUzNDA1NDgsImlhdCI6MTczMjc0ODU0OH0.t6bHsAZhAeqLxLY9KZzjCxs-_zfXRcGcHgUdNgXakHc	2024-11-27 23:02:28.517489+00
5	3	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozLCJleHAiOjE3MzUzODg2NjYsImlhdCI6MTczMjc5NjY2Nn0.wtzq1phWHw_zqVvE7Prhf0uD--CeyUxlzF79gKlYSFQ	2024-11-28 12:24:26.565828+00
1	1	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJleHAiOjE3MzYwMTc0NTYsImlhdCI6MTczMzQyNTQ1Nn0.YkH4Fc3Kz3F-ogCbcMaeAE-0fKQ3l8hyW3YP-9kV5Es	2024-11-27 23:01:48.673563+00
100	4	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo0LCJleHAiOjE3MzY3NjY4NjksImlhdCI6MTczNDE3NDg2OX0.OVo_ShxSNoZn8pXZllGUL9Fi4aTw4gLeceq49Y4wKKM	2024-12-14 11:14:29.986007+00
101	5	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1LCJleHAiOjE3MzY3Njk1MjQsImlhdCI6MTczNDE3NzUyNH0.afDdALyB5v7MU84szsJUJ9HRwr-U3DUlkuLs2uE2Lho	2024-12-14 11:58:44.574458+00
\.


--
-- Data for Name: expo_tokens; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.expo_tokens (id, token, created_at) FROM stdin;
\.


--
-- Data for Name: modules; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.modules (id, name, theory_ids, question_ids, level, created_at, pre_requisite_ids) FROM stdin;
1	Приветствие	{1}	{7,8,9,10,11,12,13,2,14,15,16,17,18,19,20}	1	2024-11-07 19:12:37.166142	\N
2	Приветствие	{1}	{7,8,9,10,11,12,13,2,14,15,16,17,18,19,20}	2	2024-12-05 15:28:34.19876	{1}
3	Приветствие	{1}	{7,8,9,10,11,12,13,2,14,15,16,17,18,19,20}	3	2024-12-05 15:28:34.19876	{2}
\.


--
-- Data for Name: progress; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.progress (id, user_id, module_id, mistaken_question_ids, "time", try_count, created_at, updated_at) FROM stdin;
48	1	1	{}	00:01:11	1	2024-12-08 14:03:54.125014	2024-12-08 14:03:54.125014
49	1	1	{}	00:01:47	2	2024-12-08 16:18:14.269498	2024-12-08 16:18:14.269498
50	5	1	{8,14}	00:05:10	1	2024-12-14 12:05:08.918826	2024-12-14 12:05:08.918826
\.


--
-- Data for Name: question_words; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.question_words (question_id, word_id, sequence_order) FROM stdin;
2	9	1
2	13	2
2	14	3
2	15	4
6	1	1
6	2	2
6	3	3
7	1	1
7	2	2
7	3	3
8	1	1
8	2	2
8	3	3
9	2	1
9	4	2
9	3	3
10	2	1
10	4	2
10	5	3
11	17	1
11	6	2
11	16	3
12	18	1
12	19	2
12	7	3
13	4	1
13	8	2
13	3	3
14	2	1
14	20	2
14	21	3
14	22	4
14	23	5
15	3	1
15	20	2
15	24	3
16	7	1
16	14	2
16	25	3
17	26	1
17	8	2
18	1	1
19	3	1
20	4	1
\.


--
-- Data for Name: questions; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.questions (id, type, correct_answer, image_url, created_at) FROM stdin;
2	build-sentence	{"sequence": [9, 13, 14, 15], "kaz_plaintext": "Cәлем, менің атым Айгуль.", "rus_plaintext": "Привет! Меня зовут Айгуль."}		2024-11-07 13:33:33.111796
6	kz-word-to-ru-word	{"sequence": [1], "kaz_plaintext": "Сәлем", "rus_plaintext": "Привет"}		2024-11-07 14:02:16.391284
8	kz-word-to-ru-word	{"sequence": [3], "kaz_plaintext": "Қайырлы күн", "rus_plaintext": "Добрый день"}		2024-11-07 14:03:09.771608
9	kz-word-to-ru-word	{"sequence": [4], "kaz_plaintext": "Кеш жарық", "rus_plaintext": "Добрый вечер"}		2024-11-07 14:03:45.552741
10	kz-word-to-ru-word	{"sequence": [5], "kaz_plaintext": "Қайырлы түн ", "rus_plaintext": "Спокойной ночи"}		2024-11-07 14:05:06.41832
11	kz-word-to-ru-word	{"sequence": [6], "kaz_plaintext": "Аты", "rus_plaintext": "Имя"}		2024-11-07 14:07:45.975784
12	kz-word-to-ru-word	{"sequence": [7], "kaz_plaintext": "Менің", "rus_plaintext": "Мой"}		2024-11-07 14:10:47.983497
13	kz-word-to-ru-word	{"sequence": [8], "kaz_plaintext": "Танысқаныма қуаныштымын", "rus_plaintext": "Приятно познакомиться"}		2024-11-07 14:12:03.539131
14	build-sentence	{"sequence": [2, 20, 21, 22, 23], "kaz_plaintext": "Cәлеметсіз бе! Мен сізді көргеніме қуаныштымын.", "rus_plaintext": "Здравствуйте! Я рад вас видеть."}		2024-11-07 14:36:19.94695
17	build-sentence	{"sequence": [26, 8], "kaz_plaintext": "Сізбен танысқаныма қуаныштымын", "rus_plaintext": "Приятно познакомиться с вами"}		2024-11-07 14:47:54.929697
15	build-sentence	{"sequence": [3, 20, 24], "kaz_plaintext": "Қайырлы күн! Мен Алексеймін", "rus_plaintext": "Добрый день! Я Алексей."}		2024-11-07 14:39:32.210562
16	build-sentence	{"sequence": [7, 14, 25], "kaz_plaintext": "Менің атым Дана", "rus_plaintext": "Меня зовут Дана"}		2024-11-07 14:43:43.128294
18	by-letter	{"sequence": [1], "kaz_plaintext": "Сәлем", "rus_plaintext": "Привет"}		2024-11-14 21:49:56.209955
19	by-letter	{"sequence": [3], "kaz_plaintext": "Қайырлы күн", "rus_plaintext": "Добрый день"}		2024-11-14 21:50:50.687574
20	by-letter	{"sequence": [4], "kaz_plaintext": "Кеш жарық", "rus_plaintext": "Добрый вечер"}		2024-11-14 21:51:13.388561
1	build-sentence	{"sequence": [1, 2], "audio_url": "https://storage.googleapis.com/audio_test-klingo/C%D3%99%D0%BB%D0%B5%D0%BC%D0%B5%D1%82%D1%81%D1%96%D0%B7%20%D0%B1%D0%B5.mp3", "kaz_plaintext": "Сәлеметсіз бе", "rus_plaintext": "Здравствуйте!"}		2024-11-07 12:55:42.40601
7	kz-word-to-ru-word	{"sequence": [2], "audio_url": "https://storage.googleapis.com/audio_test-klingo/C%D3%99%D0%BB%D0%B5%D0%BC%D0%B5%D1%82%D1%81%D1%96%D0%B7%20%D0%B1%D0%B5.mp3", "kaz_plaintext": "Сәлеметсіз бе?", "rus_plaintext": "Здравствуйте"}		2024-11-07 14:02:36.10302
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.schema_migrations (version, dirty) FROM stdin;
10	f
\.


--
-- Data for Name: sentences; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.sentences (id, kaz_plaintext, rus_plaintext, audio_url) FROM stdin;
1	Cәлеметсіз бе?	Здравствуйте!	https://example.com/emhana-audio
2	Сәлем! Менің атым Олжас.	Привет! Меня зовут Олжас.	https://example.com/emhana-audio
3	Қайырлы күн! Мен сізбен танысқаныма қуаныштымын.	Добрый день! Я рад знакомству с вами.	https://example.com/emhana-audio
\.


--
-- Data for Name: theories; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.theories (id, title, description, module_id, tags, examples_ids, created_at) FROM stdin;
1	Порядок слов в приветствии и представлении	В казахском языке порядок слов в приветствии обычно следующий:	1	{Подлежащее,Cказуемое,Объект}	{1,2,3}	2024-11-07 15:07:36.924942
\.


--
-- Data for Name: tokens; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.tokens (hash, user_id, expiry, scope) FROM stdin;
\.


--
-- Data for Name: user_achievements; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.user_achievements (id, user_id, achievement_id, progress, achieved, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.users (id, created_at, name, image_url, surname, email, password_hash, activated) FROM stdin;
1	2024-10-31 18:45:29+00	Olzhas		Mukhanbetzhan	<EMAIL>	\\x2432612431322463655a5159425855624671545a51324563374a4f794f304f6e4770544845594c6e506e577a54662f79365147464c4853436c4c6269	f
2	2024-11-27 23:02:29+00	test		test	<EMAIL>	\\x24326124313224714246447774636e7857526c58716d3647493542732e6f2e686835586473563962685062633246614e424f62394174507152355269	f
3	2024-11-28 12:24:27+00	Olzzhek		M	<EMAIL>	\\x2432612431322442467174416c3147477646665a5246775a6f6d78544f79357237315a533048694a326e37494a77434546753279394a463057576347	f
4	2024-12-14 11:14:30+00	Maksat		b	<EMAIL>	\\x24326124313224436357644131696561354f33774f6a425574673449755758456a4b45524479556444697967434a33324b316859792f79775a444343	f
5	2024-12-14 11:58:45+00	Olzhas		Mukhanbetzhan	<EMAIL>	\\x24326124313224496c595a45394e2f6161686d532f5478434b43504d4f6b5135465168425a6b445936595a466755616430373158427a5658626b6169	f
\.


--
-- Data for Name: words; Type: TABLE DATA; Schema: public; Owner: olzzhas
--

COPY public.words (id, kaz_plaintext, rus_plaintext, audio_url) FROM stdin;
7	Менің	Мой	https://example.com/emhana-audio
8	Танысқаныма қуаныштымын	Приятно познакомиться	https://example.com/emhana-audio
9	сәлем!	привет! 	https://example.com/emhana-audio
13	менің	мое	https://example.com/emhana-audio
14	атым	имя	https://example.com/emhana-audio
15	Айгуль	Айгуль	https://example.com/emhana-audio
16	Жас	Возраст	https://example.com/emhana-audio
17	Салмақ	Вес	https://example.com/emhana-audio
18	Сенің	Твой	https://example.com/emhana-audio
19	Оның	Его	https://example.com/emhana-audio
20	мен	я	https://example.com/emhana-audio
21	сізді	вас	https://example.com/emhana-audio
22	көргеніме	видеть	https://example.com/emhana-audio
23	қуаныштымын	рад	https://example.com/emhana-audio
24	Алексеймін	Алексей	https://example.com/emhana-audio
25	Дана	Дана	https://example.com/emhana-audio
26	сізбен	с вами	https://example.com/emhana-audio
1	Сәлем	Привет	https://storage.googleapis.com/audio_test-klingo/%D1%81%D3%99%D0%BB%D0%B5%D0%BC.mp3
2	Сәлеметсіз бе!	Здравствуйте	https://storage.googleapis.com/audio_test-klingo/%D2%9A%D0%B0%D0%B9%D1%8B%D1%80%D0%BB%D1%8B%20%D0%BA%D2%AF%D0%BD.mp3
3	Қайырлы күн	Добрый день	https://storage.googleapis.com/audio_test-klingo/%D2%9A%D0%B0%D0%B9%D1%8B%D1%80%D0%BB%D1%8B%20%D0%BA%D2%AF%D0%BD.mp3
4	Кеш жарық	Добрый вечер	https://storage.googleapis.com/audio_test-klingo/%D0%9A%D0%B5%D1%88%20%D0%B6%D0%B0%D1%80%D1%8B%D2%9B.mp3
5	Қайырлы түн	Спокойной ночи	https://storage.googleapis.com/audio_test-klingo/%D2%9A%D0%B0%D0%B9%D1%8B%D1%80%D0%BB%D1%8B%20%D1%82%D2%AF%D0%BD.mp3
6	Аты	Имя	https://storage.googleapis.com/audio_test-klingo/%D0%90%D1%82%D1%8B.mp3
\.


--
-- Name: achievements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.achievements_id_seq', 8, true);


--
-- Name: authorization_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.authorization_tokens_id_seq', 101, true);


--
-- Name: expo_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.expo_tokens_id_seq', 1, false);


--
-- Name: modules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.modules_id_seq', 1, true);


--
-- Name: progress_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.progress_id_seq', 50, true);


--
-- Name: questions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.questions_id_seq', 22, true);


--
-- Name: sentences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.sentences_id_seq', 3, true);


--
-- Name: theories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.theories_id_seq', 1, true);


--
-- Name: user_achievements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.user_achievements_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.users_id_seq', 5, true);


--
-- Name: words_id_seq; Type: SEQUENCE SET; Schema: public; Owner: olzzhas
--

SELECT pg_catalog.setval('public.words_id_seq', 26, true);


--
-- Name: achievements achievements_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.achievements
    ADD CONSTRAINT achievements_pkey PRIMARY KEY (id);


--
-- Name: authorization_tokens authorization_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.authorization_tokens
    ADD CONSTRAINT authorization_tokens_pkey PRIMARY KEY (id);


--
-- Name: expo_tokens expo_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.expo_tokens
    ADD CONSTRAINT expo_tokens_pkey PRIMARY KEY (id);


--
-- Name: expo_tokens expo_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.expo_tokens
    ADD CONSTRAINT expo_tokens_token_key UNIQUE (token);


--
-- Name: modules modules_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT modules_pkey PRIMARY KEY (id);


--
-- Name: progress progress_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.progress
    ADD CONSTRAINT progress_pkey PRIMARY KEY (id);


--
-- Name: question_words question_words_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.question_words
    ADD CONSTRAINT question_words_pkey PRIMARY KEY (question_id, word_id);


--
-- Name: questions questions_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.questions
    ADD CONSTRAINT questions_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sentences sentences_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.sentences
    ADD CONSTRAINT sentences_pkey PRIMARY KEY (id);


--
-- Name: theories theories_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.theories
    ADD CONSTRAINT theories_pkey PRIMARY KEY (id);


--
-- Name: tokens tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.tokens
    ADD CONSTRAINT tokens_pkey PRIMARY KEY (hash);


--
-- Name: user_achievements user_achievements_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT user_achievements_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: words words_pkey; Type: CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.words
    ADD CONSTRAINT words_pkey PRIMARY KEY (id);


--
-- Name: unique_user_token; Type: INDEX; Schema: public; Owner: olzzhas
--

CREATE UNIQUE INDEX unique_user_token ON public.authorization_tokens USING btree (user_id);


--
-- Name: authorization_tokens authorization_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.authorization_tokens
    ADD CONSTRAINT authorization_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: question_words question_words_question_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.question_words
    ADD CONSTRAINT question_words_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.questions(id) ON DELETE CASCADE;


--
-- Name: question_words question_words_word_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.question_words
    ADD CONSTRAINT question_words_word_id_fkey FOREIGN KEY (word_id) REFERENCES public.words(id) ON DELETE CASCADE;


--
-- Name: tokens tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.tokens
    ADD CONSTRAINT tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_achievements user_achievements_achievement_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT user_achievements_achievement_id_fkey FOREIGN KEY (achievement_id) REFERENCES public.achievements(id) ON DELETE CASCADE;


--
-- Name: user_achievements user_achievements_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: olzzhas
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT user_achievements_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

